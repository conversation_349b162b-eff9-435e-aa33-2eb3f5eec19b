import { NextFunction, Request, Response } from "express";
import { ControllerType } from "../types/types.js";
import <PERSON><PERSON>r<PERSON><PERSON><PERSON> from "../utils/utility-class.js";

export const errorMiddleware = (
  err: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  err.message ||= "Internal Server Error";
  err.statusCode ||= 500;
  if (err.name==="CastError") err.message="Invalid Id";

  res.status(err.statusCode).json({
    success: false,
    message: err.message,
  });
};

export const TryCatch = (func: ControllerType) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(func(req, res, next)).catch(next);
  };
};

// export const TryCatch = (func: ControllerType) => {
//   return (req: Request, res: Response, next: NextFunction) => {
//     return Promise.resolve(func(req, res, next)).catch(next);
//   };
// };
