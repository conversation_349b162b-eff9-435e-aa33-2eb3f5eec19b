.dashboard {
	overflow-y: auto;

	.bar {
		height: 4rem;
		@include flex(row, unset);
		padding: 0 1rem;
		border-bottom: 1px solid rgba(0, 0, 0, 0.37);
		> input {
			margin-right: auto;
			@include inputStyle(100%, 1rem 0);
		}
		> svg {
			font-size: 1.2rem;
			opacity: 0.7;
		}
		> img {
			@include square(2rem);
			border-radius: 50%;
		}
	}

	.widget-container {
		@include flex(row, space-between, stretch, 2rem);
		padding: 2rem 2rem 2rem 0;

		.widget {
			width: 16rem;
			background-color: white;
			box-shadow: 0 0 10px rgba(0, 0, 0, 0.132);
			padding: 2rem;
			border-radius: 10px;
			@include flex(row, space-between, stretch, 0);

			.widget-info {
				> p {
					opacity: 0.7;
					font-size: 0.8rem;
				}
				> h4 {
					font-size: 1.5rem;
				}
				> span {
					@include flex(row, unset, center, 0.2rem);
				}
			}

			.widget-circle {
				position: relative;
				@include square(5rem);
				border-radius: 50%;
				flex: none;
				display: grid;
				place-items: center;
				background-color: aquamarine;
				&::before {
					content: "";
					position: absolute;
					@include square(4rem);
					background-color: white;
					border-radius: 100%;
				}
				span {
					position: relative;
				}
			}
		}
	}

	.graph-container {
		@include flex(row, unset, unset, 2rem);
		padding: 0 2rem 2rem 0;
		> div {
			background-color: white;
			border-radius: 10px;
		}

		.revenue-chart {
			width: 100%;
			padding: 1rem 3rem;

			> h2 {
				@include heading;
				margin: 1rem 0 2rem 0.25rem;
				text-align: center;
			}
		}

		.dashboard-categories {
			width: 100%;
			max-width: 16rem;
			@include flex(column, center, unset, 0);
			padding-bottom: 2rem;
			> h2 {
				@include heading;
				margin: 1.5rem 0 2rem 0;
				text-align: center;
			}

			> div {
				overflow-y: auto;
				height: 40rem;
				padding-left: 0.5rem;
				&::-webkit-scrollbar {
					display: none;
				}
			}

			.category-item {
				width: 100%;
				@include flex(row, space-between);
				padding: 1rem;
				> h5 {
					letter-spacing: 1px;
					font-weight: 300;
				}
				> div {
					margin-left: auto;
					width: 6rem;
					background-color: rgb(217, 217, 217);
					border-radius: 20px;
					height: 0.5rem;
					flex: none;
					> div {
						border-radius: 20px;
						height: 100%;
					}
				}
				> span {
					font-size: 0.8rem;
					font-weight: 700;
				}
			}
		}
	}

	.transaction-container {
		display: flex;
		gap: 2rem;
		padding: 0 2rem 2rem 0;
		height: 30rem;

		> div {
			background-color: white;
			box-shadow: 0px 10px 10px rgba(0, 0, 0, 0.132);
			border-radius: 10px;
		}
		.gender-chart {
			width: 100%;
			max-width: 20rem;
			padding: 1rem;
			position: relative;
			> h2 {
				text-align: center;
				margin: 1.5rem 0 2rem 0;
				@include heading;
			}
			> p {
				@include posCenter;
				font-size: 2rem;
				color: rgba(0, 0, 0, 0.634);
			}
		}

		.transaction-box {
			width: 100%;
			padding: 1rem;
			overflow-x: auto;
			> h2 {
				margin: 1.5rem 0 0 0;
				@include heading;
			}
		}
	}
}
