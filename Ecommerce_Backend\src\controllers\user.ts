import { NextFunction, Request, Response } from "express";
import User from "../models/user.js";
import { NewUserRequestBody } from "../types/types.js";
import { TryCatch } from "../middlewares/error.js";
import ErrorHandler from "../utils/utility-class.js";

export const newUser = TryCatch(
	async (
		req: Request<{}, {}, NewUserRequestBody>,
		res: Response,
		next: NextFunction
	) => {
		try {
			const { name, email, dob, photo, gender, _id } = req.body;
			console.log("Received user data:", { name, email, dob, gender, _id });

			// Validate required fields
			if (!name || !email || !dob || !photo || !gender || !_id) {
				console.log("Missing required fields");
				return next(new ErrorHandler("Please Enter All Fields", 400));
			}

			// Check if user already exists
			let user = await User.findById(_id);

			if (user) {
				console.log("User already exists:", user.name);
				return res.status(200).json({
					success: true,
					message: `Welcome again ${user.name}`,
				});
			}

			// Parse date of birth
			let parsedDob;
			try {
				parsedDob = new Date(dob);
				if (isNaN(parsedDob.getTime())) {
					console.log("Invalid date format:", dob);
					return next(new ErrorHandler("Invalid date of birth format", 400));
				}
			} catch (dateError) {
				console.error("Error parsing date:", dateError);
				return next(new ErrorHandler("Invalid date of birth", 400));
			}

			// Create new user
			try {
				const userData = {
					name,
					email,
					dob: parsedDob,
					photo,
					gender,
					_id,
				};
				console.log("Creating user with data:", userData);

				user = await User.create(userData);

				console.log("User created successfully:", user.name);
				return res.status(201).json({
					success: true,
					message: `Welcome ${user.name}`,
				});
			} catch (error: any) {
				console.error("Error creating user:", error);
				if (error.name === 'ValidationError') {
					return next(new ErrorHandler("Validation Error: " + error.message, 400));
				}
				return next(new ErrorHandler("Error creating user", 500));
			}
		} catch (error) {
			console.error("Unexpected error in newUser:", error);
			return next(new ErrorHandler("Server Error", 500));
		}
	}
);

export const getAllUsers = TryCatch(async (req, res, next) => {
	let users = await User.find({});

	return res.status(200).json({
		success: true,
		users,
	});
});

export const getUser = TryCatch(async (req, res, next) => {
	let id = req.params.id;
	console.log("Requested user ID:", id);

	// Validate ID exists
	if (!id) {
		return next(new ErrorHandler("User ID is required", 400));
	}

	try {
		// Firebase UIDs are typically 28 characters long
		let user = await User.findById(id);
		if (!user) {
			console.log("User not found with ID:", id);
			return next(new ErrorHandler("User not found", 404));
		}

		console.log("User found:", user.name);
		return res.status(200).json({
			success: true,
			user,
		});
	} catch (error) {
		console.error("Error retrieving user:", error);
		return next(new ErrorHandler("Error retrieving user", 500));
	}
});

export const deleteUser = TryCatch(async (req, res, next) => {
	let id = req.params.id;
	let user = await User.findById(id);

	if (!user) return next(new ErrorHandler("Invalid Id", 400));
	await user.deleteOne();

	return res.status(200).json({
		success: true,
		message: "User deleted successfully",
	});
});
