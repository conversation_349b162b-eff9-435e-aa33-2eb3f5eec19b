# Node.js dependencies
/node_modules
/.pnp
.pnp.js

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul/jest
coverage
*.lcov

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# dotenv environment variables file
.env
.env.local
.env.*.local

# Database and local environment files
*.sqlite
*.db
*.db-journal

# OS-specific files
.DS_Store
Thumbs.db

# IDE and editor settings
.idea/
.vscode/
*.sublime-workspace
*.sublime-project

# Build and output directories
/dist
/build
/.next
/out

# TypeScript cache and output files
*.tsbuildinfo

# Generated files
public/static
public/assets
