import { ChangeEvent, useState } from "react";
import AdminSidebar from "../../../components/admin/AdminSidebar";
import { useSelector } from "react-redux";
import { UserReducerInitialState } from "../../../types/reducer-types";
import {
	useAllProductsQuery,
	useNewProductMutation,
} from "../../../redux/api/productApi";
import { useNavigate } from "react-router-dom";
import { responseToast } from "../../../utils/features";

const NewProduct = () => {
	const { user } = useSelector(
		(state: { user: UserReducerInitialState }) => state.user
	);
	const { refetch } = useAllProductsQuery(user?._id ?? "");
	const [name, setName] = useState<string>("");
	const [category, setCategory] = useState<string>("");
	const [price, setPrice] = useState<number>(1000);
	const [stock, setStock] = useState<number>(1);
	const [photoPrev, setPhotoPrev] = useState<string>("");
	const [photo, setPhoto] = useState<File>();

	const [newProduct] = useNewProductMutation();
	const navigate = useNavigate();

	const changeImageHandler = (e: ChangeEvent<HTMLInputElement>) => {
		const file: File | undefined = e.target.files?.[0];

		const reader: FileReader = new FileReader();

		if (file) {
			reader.readAsDataURL(file);
			reader.onloadend = () => {
				if (typeof reader.result === "string") {
					setPhotoPrev(reader.result);
					setPhoto(file);
				}
			};
		}
	};

	const submitHandler = async (e: React.FormEvent<HTMLFormElement>) => {
		e.preventDefault();

		if (!photo || !name || !category || !price || stock < 1) {
			return;
		}

		const formData = new FormData();

		formData.set("name", name);
		formData.set("price", price.toString());
		formData.set("stock", stock.toString());
		formData.set("category", category);
		formData.set("photo", photo);
		try {
			const response = await newProduct({
				formData,
				id: user?._id ?? "",
			});

			console.log("API Response:", response);
			refetch(); // Refetch the product list
			responseToast(response, navigate, "/admin/product");
		} catch (error) {
			console.error("Error creating product:", error);
		}
	};
	return (
		<div className="admin-container">
			<AdminSidebar />
			<main className="product-management">
				<article>
					<form onSubmit={submitHandler}>
						<h2>New Product</h2>
						<div>
							<label>Name</label>
							<input
								type="text"
								placeholder="Name"
								value={name}
								onChange={(e) => setName(e.target.value)}
							/>
						</div>
						<div>
							<label>Price</label>
							<input
								required
								type="number"
								placeholder="Price"
								value={price}
								onChange={(e) => setPrice(Number(e.target.value))}
							/>
						</div>
						<div>
							<label>Stock</label>
							<input
								required
								type="number"
								placeholder="Stock"
								value={stock}
								onChange={(e) => setStock(Number(e.target.value))}
							/>
						</div>

						<div>
							<label>Category</label>
							<input
								required
								type="text"
								placeholder="eg. laptop, camera etc"
								value={category}
								onChange={(e) => setCategory(e.target.value)}
							/>
						</div>

						<div>
							<label>Photo</label>
							<input type="file" required onChange={changeImageHandler} />
						</div>

						{photoPrev && <img src={photoPrev} alt="New Image" />}
						<button type="submit">Create</button>
					</form>
				</article>
			</main>
		</div>
	);
};

export default NewProduct;
