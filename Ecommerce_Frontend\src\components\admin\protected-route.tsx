import { ReactElement } from "react";
import { Navigate, Outlet } from "react-router-dom";

interface Props {
  children?: ReactElement;
  isAuthenticated: boolean;
  adminOnly?: boolean;
  admin?: boolean;
  redirect?: string;
}

const ProtectedRoute = ({
  isAuthenticated,
  children,
  adminOnly,
  admin,
  redirect = "/",
}: Props) => {
  // Redirect to specified path if not authenticated
  if (!isAuthenticated) return <Navigate to={redirect} />;

  // Redirect if admin access is required but user is not an admin
  if (adminOnly && !admin) return <Navigate to={redirect} />;

  // Return children if provided, otherwise render the Outlet for nested routes
  return children ? children : <Outlet />;
};

export default ProtectedRoute;
