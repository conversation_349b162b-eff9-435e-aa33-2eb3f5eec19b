.login{
   height: 90vh;
   @include flex(column);
   
   >main{
      width: 100%;
      height: 80%;
      max-width: 400px;
      padding: 2rem;

      @include baseShadow;
      @include flex(column,center,stretch);
      >div{
         width: 100%;
         @include flex(column,flex-start,stretch,0.2rem);
         >input,>select{
            @include inputStyle;
           border: 2px solid rgba(162,162,162,0.53);
           border-radius: 5px;
           font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
           
         }
         >p{
            text-align: center;
            margin: 2rem;
         }
         button{
           margin: auto;
            width: 70%;
           height: 3rem;
           @include btn;
           @include flex;
           border-radius: 5px;
           font-size: 1.05rem;
           font-weight: 600;
           >svg{
            background-color: white;
            width:30%;
            height:100%;
           }
           >span{
            width:100%;
            text-align: center;
           }
         }
      }
   }
}