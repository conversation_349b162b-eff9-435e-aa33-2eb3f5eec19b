.home {
  padding: 1rem 2rem;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 4rem);
  width: 100%;
  > section {
    width: 100%;
    height: 15rem;
    margin: auto;
    background: url("../assets/images/cover.jpg") no-repeat center / cover;
  }
  > h1 {
    @include heading(1px);
    margin-top: 2rem;
    @include flex(row, space-between, center);
  }

  .findmore {
    font-size: 1rem;
  }
  > main {
    width: 100%;
    flex: 1;
    display: flex;
    gap: 1rem;
    overflow-x: auto;

    &::-webkit-scrollbar {
      display: none;
    }
  }
}

.product-card {
  width: 15rem;
  height: 20rem;
  flex: none;
  padding: 1rem;
  @include flex(column, flex-start, center, 4px);
position: relative;
background-color: white;
&:hover >div{
   opacity: 1;
}
  > img {
    @include square(calc(15rem - 2rem));
    object-fit: cover;
    margin: 1rem;
  }
  span {
    font-weight: 700;
    font-size: 1.1rem;
  }
  > div {
   opacity: 0;
    position: absolute;
    @include square(100%);
    top: 0;
    left: 0;
    background-color: rgba(0, 0, 0, 0.5);
    @include flex;
    button {
      @include flex;
      @include square(3rem);
      border-radius: 100%;
      border: none;
      background-color: $color3;
      cursor: pointer;
      font-size: 1.1rem;
      transition: all 0.3s;
      &:hover {
       
        rotate: 90deg ;
      
    transition-duration: 0.35s;
      }
    }
  }
}
