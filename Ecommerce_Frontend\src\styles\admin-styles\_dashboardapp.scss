.dashboard-app-container {
  background-color: white;
  padding: 4rem;

  > section {
    @include flex(column, center, center, 2rem);
    height: 100%;

    .stopwatch {
      > h2 {
        font-size: 2rem;
        font-weight: 300;
        text-align: center;
      }

      > button {
        padding: 1rem 2rem;
        border: none;
        cursor: pointer;
        color: white;
        margin: 2rem;
        font-weight: 700;
        border-radius: 10px;

        &:first-of-type {
          background-color: rgb(0, 98, 255);
        }

        &:last-of-type {
          background-color: rgb(255, 0, 0);
        }
      }
    }

    .tosscoin {
      margin: 2rem;
      @include square(15rem);
      position: relative;
      cursor: pointer;
      transform-style: preserve-3d;
      transition: all 0.5s;
      > div {
        border-radius: 50%;
        @include square(100%);
        position: absolute;
        display: grid;
        place-items: center;
        background-repeat: no-repeat;
        background-size: contain;
        backface-visibility: hidden;
        filter: drop-shadow(0px 10px 10px rgba(0, 0, 0, 0.521));
        &:first-of-type {
          background-image: url("../assets/images/heads.png");
        }
        &:last-of-type {
          background-image: url("../assets/images/tails.png");
          transform: rotateY(-180deg);
        }
      }
    }

    .coupon-form {
      @include grid(2fr 1fr, 2rem);
      max-width: 30rem;
      width: 100%;
      > input {
        padding: 1rem;
        border: 1px solid rgba(0, 0, 0, 0.359);
        outline: none;
        border-radius: 5px;
      }

      > fieldset {
        padding: 1rem;
        border: 1px solid rgba(0, 0, 0, 0.359);
        border-radius: 5px;
        @include flex(row, center, center, 0);
        flex-wrap: wrap;
        grid-column: 1/3;

        span {
          font-size: 0.8rem;
          font-weight: 300;
          margin-inline-start: 0.25rem;
          margin-inline-end: 1rem;
        }
      }

      > button {
        font-weight: 700;
        font-size: 1.1rem;
        width: 100%;
        padding: 1rem;
        border: none;
        color: white;
        cursor: pointer;
        margin: 2rem 0;
        border-radius: 10px;
        grid-column: 1/3;
        background-color: rgb(0, 98, 255);
      }
    }

    > code {
      position: relative;
      font-size: 1.2rem;
      letter-spacing: 2px;
      cursor: pointer;
      &:hover > span {
        opacity: 1;
      }

      > span {
        opacity: 0;
        @include square(100%);
        top: 0;
        left: 0;
        position: absolute;
        border-radius: 5px;
        background-color: rgb(15, 15, 15);
        color: white;
        font-size: 0.8rem;
        @include flex;
      }
    }
  }
}
