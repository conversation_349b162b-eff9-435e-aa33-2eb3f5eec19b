export const errorMiddleware = (err, req, res, next) => {
    err.message || (err.message = "Internal Server Error");
    err.statusCode || (err.statusCode = 500);
    if (err.name === "CastError")
        err.message = "Invalid Id";
    res.status(err.statusCode).json({
        success: false,
        message: err.message,
    });
};
export const TryCatch = (func) => {
    return (req, res, next) => {
        Promise.resolve(func(req, res, next)).catch(next);
    };
};
// export const TryCatch = (func: ControllerType) => {
//   return (req: Request, res: Response, next: NextFunction) => {
//     return Promise.resolve(func(req, res, next)).catch(next);
//   };
// };
