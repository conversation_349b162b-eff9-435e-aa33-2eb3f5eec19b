{"name": "ecommerce", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@reduxjs/toolkit": "^2.3.0", "@stripe/react-stripe-js": "^3.4.0", "@types/stripe-v3": "^3.1.33", "axios": "^1.7.7", "chart.js": "^4.4.6", "firebase": "^10.13.1", "react": "^18.3.1", "react-chartjs-2": "^5.2.0", "react-content-loader": "^7.0.2", "react-dom": "^18.3.1", "react-hot-toast": "^2.4.1", "react-icons": "^5.3.0", "react-loading-skeleton": "^3.5.0", "react-redux": "^9.1.2", "react-router-dom": "^6.26.1", "react-select": "^5.8.2", "react-select-country-list": "^2.2.3", "react-table": "^7.8.0", "sass": "^1.78.0"}, "devDependencies": {"@eslint/js": "^9.9.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/react-select-country-list": "^2.2.3", "@types/react-table": "^7.7.20", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1"}}